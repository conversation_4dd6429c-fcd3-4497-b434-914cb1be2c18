{"name": "gloopi-backend", "version": "0.1.0", "description": "this is the server of dashter built with node, express, mongoose schema validation and use mongodb as a database", "main": "index.js", "scripts": {"dev": "nodemon api/index.js", "start": "node api/index.js", "production": "NODE_ENV=production nodemon api/index.js", "test": "echo \"Error: no test specified\" && exit 1", "data:import": "node script/seed.js", "product": "node script/product.js", "generate-password": "node script/script.js"}, "author": "<PERSON>", "license": "Regular", "dependencies": {"@paypal/checkout-server-sdk": "^1.0.3", "axios": "^1.6.7", "bcryptjs": "^2.4.3", "body-parser": "^1.19.0", "cloudinary": "^1.26.3", "cors": "^2.8.5", "dayjs": "^1.10.7", "dotenv": "^10.0.0", "express": "^4.17.1", "express-rate-limit": "^6.1.0", "helmet": "^5.0.2", "js-cookie": "^3.0.1", "jsonwebtoken": "^9.0.0", "mailchecker": "^6.0.15", "mongoose": "^5.13.8", "mongoose-sequence": "^5.3.1", "node-fetch": "^2.7.0", "nodemailer": "^6.7.2", "pdfkit": "^0.16.0", "razorpay": "^2.9.4", "socket.io": "^4.7.2", "stripe": "^9.14.0", "twilio": "^5.4.2", "uuid": "^8.3.2"}, "devDependencies": {"nodemon": "^3.1.0"}}