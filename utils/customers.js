const bcrypt = require("bcryptjs");

const customers = [
  {
    _id: "6439713c1d8869133e8881e3",

    name: "<PERSON>",
    email: "<EMAIL>",
    password: bcrypt.hashSync("12345678"),
    phone: "************",
  },
  {
    _id: "6439713c1d8869133e8881f2",

    name: "<PERSON>",
    email: "<EMAIL>",
    password: bcrypt.hashSync("12345678"),
    phone: "************",
  },
  {
    _id: "6439713c1d8869133e8881e8",

    name: "<PERSON>",
    email: "<EMAIL>",
    password: bcrypt.hashSync("12345678"),
    phone: "************",
  },
  {
    _id: "6439713c1d8869133e8881e6",

    name: "<PERSON>",
    email: "<EMAIL>",
    password: bcrypt.hashSync("12345678"),
    phone: "************",
  },
  {
    _id: "6439713c1d8869133e8881e9",

    name: "<PERSON> J. <PERSON>",
    email: "<EMAIL>",
    password: bcrypt.hashSync("12345678"),
    phone: "************",
  },
  {
    _id: "6439713c1d8869133e8881e4",

    name: "Jon B. Krueger",
    email: "<EMAIL>",
    password: bcrypt.hashSync("12345678"),
    phone: "************",
  },
  {
    _id: "6439713c1d8869133e8881ef",

    name: "Gordon C. Lowery",
    email: "<EMAIL>",
    password: bcrypt.hashSync("12345678"),
    phone: "************",
  },
  {
    _id: "6439713c1d8869133e8881ee",

    name: "Lester J. Massey",
    email: "<EMAIL>",
    password: bcrypt.hashSync("12345678"),
    phone: "************",
  },
  {
    _id: "6439713c1d8869133e8881eb",

    name: "Kathryn J. Brown",
    email: "<EMAIL>",
    password: bcrypt.hashSync("12345678"),
    phone: "************",
  },
  {
    _id: "6439713c1d8869133e8881f1",

    name: "Eddie N. Garcia",
    email: "<EMAIL>",
    password: bcrypt.hashSync("12345678"),
    phone: "************",
  },
  {
    _id: "6439713c1d8869133e8881e7",

    name: "Christopher M. Fox",
    email: "<EMAIL>",
    password: bcrypt.hashSync("12345678"),
    phone: "************",
  },
  {
    _id: "6439713c1d8869133e8881ed",

    name: "Samuel",
    email: "<EMAIL>",
    password: bcrypt.hashSync("12345678"),
    phone: "************",
  },
  {
    _id: "6439713c1d8869133e8881f6",

    name: "Richard E. Romero",
    email: "<EMAIL>",
    password: bcrypt.hashSync("12345678"),
    phone: "************",
  },
  {
    _id: "6439713c1d8869133e8881ea",

    name: "Josephine M. Peel",
    email: "<EMAIL>",
    password: bcrypt.hashSync("12345678"),
    phone: "************",
  },
  {
    _id: "6439713c1d8869133e8881ec",

    name: "Henry M. Koch",
    email: "<EMAIL>",
    password: bcrypt.hashSync("12345678"),
    phone: "************",
  },
  {
    _id: "6439713c1d8869133e8881f0",

    name: "Williams",
    email: "<EMAIL>",
    password: bcrypt.hashSync("12345678"),
    phone: "************",
  },
  {
    _id: "6439713c1d8869133e8881f3",

    name: "Danielle R. Martin",
    email: "<EMAIL>",
    password: bcrypt.hashSync("12345678"),
    phone: "************",
  },
  {
    _id: "6439713c1d8869133e8881e5",

    name: "Hilary W. Becker",
    email: "<EMAIL>",
    password: bcrypt.hashSync("12345678"),
    phone: "************",
  },
  {
    _id: "6439713c1d8869133e8881f4",

    name: "Thomas",
    email: "<EMAIL>",
    password: bcrypt.hashSync("12345678"),
    phone: "************",
  },
  {
    _id: "6439713c1d8869133e8881f5",

    name: "Brain H. Landry",
    email: "<EMAIL>",
    password: bcrypt.hashSync("12345678"),
    phone: "************",
  },
];
module.exports = customers;
