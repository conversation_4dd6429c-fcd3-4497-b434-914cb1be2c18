const bcrypt = require("bcryptjs");
const admins = [
  {
    name: {
      en: "<PERSON>",
    },
    image: "https://i.ibb.co/d294W8Y/team-4.jpg",
    email: "<EMAIL>",
    password: bcrypt.hashSync("********"),
    phone: "************",
    role: "Security Guard",
    joiningData: new Date(),
  },
  {
    name: {
      en: "<PERSON>",
    },
    image: "https://i.ibb.co/m5B0hK4/team-8.jpg",
    email: "<EMAIL>",
    password: bcrypt.hashSync("********"),
    phone: "************",
    role: "Cashier",
    joiningData: new Date(),
    access_list: [
      "dashboard",
      "orders",
      "order",
      "languages",
      "currencies",
      "notifications",
      "edit-profile",
      "coming-soon",
      "customers",
      "customer-order",
    ],
  },
  {
    name: {
      en: "<PERSON><PERSON>",
    },
    image: "https://i.ibb.co/SNN7JCX/team-6.jpg",
    email: "<EMAIL>",
    password: bcrypt.hashSync("********"),
    phone: "************",
    role: "Accountant",
    joiningData: new Date(),
  },
  {
    name: {
      en: "Shawn E. Palmer",
    },
    image: "https://i.ibb.co/GWVWYNn/team-7.jpg",
    email: "<EMAIL>",
    password: bcrypt.hashSync("********"),
    phone: "************",
    role: "Manager",
    joiningData: new Date(),
  },
  {
    name: {
      en: "Stacey J. Meikle",
    },
    image: "https://i.ibb.co/XjwBLcK/team-2.jpg",
    email: "<EMAIL>",
    password: bcrypt.hashSync("********"),
    phone: "************",
    role: "CEO",
    joiningData: new Date(),
  },
  {
    name: {
      en: "Marion V. Parker",
    },
    image: "https://i.ibb.co/3zs3H7z/team-5.jpg",
    email: "<EMAIL>",
    password: bcrypt.hashSync("********"),
    phone: "************",
    role: "Admin",
    joiningData: new Date(),
    access_list: [
      "dashboard",
      "products",
      "categories",
      "attributes",
      "coupons",
      "customers",
      "orders",
      "our-staff",
      "settings",
      "languages",
      "currencies",
      "store",
      "customization",
      "store-settings",
      "notifications",
    ],
  },
  {
    name: {
      en: "Admin",
    },
    image: "https://i.ibb.co/WpM5yZZ/9.png",
    email: "<EMAIL>",
    password: bcrypt.hashSync("********"),
    phone: "************",
    role: "Super Admin",
    joiningData: new Date(),
    access_list: [
      "dashboard",
      "products",
      "product",
      "categories",
      "attributes",
      "coupons",
      "orders",
      "order",
      "our-staff",
      "settings",
      "languages",
      "currencies",
      "store",
      "customization",
      "store-settings",
      "notifications",
      "edit-profile",
      "coming-soon",
      "customers",
      "customer-order",
    ],
  },
];

module.exports = admins;
