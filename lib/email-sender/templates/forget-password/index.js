const forgetPasswordEmailBody = (option) => {
  return `
<html
  xmlns="http://www.w3.org/1999/xhtml"
  xmlns:v="urn:schemas-microsoft-com:vml"
  xmlns:o="urn:schemas-microsoft-com:office:office"
>

  <head>
    <title>Gloopi</title>
    <!--[if !mso]><!-->
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <!--<![endif]-->
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type="text/css">
      #outlook a { padding: 0; } body { margin: 0; padding: 0;
      -webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; } table, td {
      border-collapse: collapse; mso-table-lspace: 0pt; mso-table-rspace: 0pt; }
      img { border: 0; height: auto; line-height: 100%; outline: none;
      text-decoration: none; -ms-interpolation-mode: bicubic; } p { display:
      block; margin: 13px 0; } #common_table{ border: 1px solid lightgrey;
      border-collapse: collapse; }

    </style>

    <style type="text/css">
      @media only screen and (min-width:480px) { .mj-column-per-100 { width:
      100% !important; max-width: 100%; } .mj-column-per-50 { width: 50%
      !important; max-width: 50%; } }

    </style>
    <style media="screen and (min-width:480px)">
      .moz-text-html .mj-column-per-100 { width: 100% !important; max-width:
      100%; } .moz-text-html .mj-column-per-50 { width: 50% !important;
      max-width: 50%; }

    </style>
    <style type="text/css">
      @media only screen and (max-width:480px) { table.mj-full-width-mobile {
      width: 100% !important; } td.mj-full-width-mobile { width: auto
      !important; } }

    </style>
  </head>

  <body style="word-spacing:normal;background-color:#f2f3f8;">
    <div style="background-color:#f2f3f8; padding-bottom:100px;">
      <table
        align="center"
        border="0"
        cellpadding="0"
        cellspacing="0"
        role="presentation"
        style="background-color:#f2f3f8;"
      >
        <tbody>
          <tr>
            <td>

              <div style="margin:30px auto;max-width:600px; height:80px">
                <table
                  align="center"
                  border="0"
                  cellpadding="0"
                  cellspacing="0"
                  role="presentation"
                  style="width:100%;"
                >
                  <tbody>
                    <tr>
                      <td
                        style="direction:ltr;font-size:0px;padding:20px 0;padding-bottom:0;text-align:center;"
                      >

                        <div
                          class="mj-column-per-100 mj-outlook-group-fix"
                          style="font-size:0px;text-align:left;direction:ltr;display:inline-block;vertical-align:top;width:100%;"
                        >
                          <table
                            border="0"
                            cellpadding="0"
                            cellspacing="0"
                            role="presentation"
                            style="vertical-align:top;"
                            width="100%"
                          >
                            <tbody>
                              <tr>
                                <td
                                  align="center"
                                  style="font-size:0px;padding:10px 25px;word-break:break-word;"
                                >
                                  <table
                                    border="0"
                                    cellpadding="0"
                                    cellspacing="0"
                                    role="presentation"
                                    style="border-collapse:collapse;border-spacing:0px;"
                                  >
                                    <tbody>
                                      <tr>
                                        <td style="width:150px;">
                                          <!-- replace image cdn -->
                                          <img
                                            alt
                                            height="auto"
                                            src="https://res.cloudinary.com/ahossain/image/upload/v1676296566/Gloopi%20files/cc_v8jite.png"
                                            style="border:0;display:block;outline:none;text-decoration:none;height:auto;width:80px;font-size:13px;padding-bottom:30px;"
                                            width="80"
                                            height="80"
                                          />
                                        </td>
                                      </tr>
                                    </tbody>
                                  </table>
                                </td>
                              </tr>
                            </tbody>
                          </table>
                        </div>
                        <!--[if mso | IE]></td></tr></table><![endif]-->
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
              <!--[if mso | IE]></td></tr></table><![endif]-->
            </td>
          </tr>
        </tbody>
      </table>

      <div
        style="background:#ffffff;background-color:#ffffff;margin:0px auto;max-width:600px;"
      >
        <table
          align="center"
          border="0"
          cellpadding="0"
          cellspacing="0"
          role="presentation"
          style="background:#ffffff;background-color:#ffffff;width:100%;"
        >
          <tbody>
            <tr>
              <td
                style="direction:ltr;font-size:0px;padding:0 0 20px;padding-left:15px;padding-right:15px;text-align:center;padding-top: 20px;"
              >

                <div
                  class="mj-column-per-100 mj-outlook-group-fix"
                  style="font-size:15px;text-align:left;direction:ltr;display:inline-block;vertical-align:top;width:100%;background:#ffffff;background-color:#ffffff"
                >
                  <!--start email_template -->

                  <!--end email_template -->
                </div>

              </td>
            </tr>
          </tbody>
        </table>

      </div>

      <div
        class="body-section"
        style="margin: 0px auto; max-width: 600px; border border-radius:4px"
      >
        <table
          align="center"
          cellpadding="0"
          cellspacing="0"
          role="presentation"
          style="width:100%;"
        >
          <tbody>
            <tr>
              <td
                style="direction:ltr;font-size:0px;padding:20px 0;padding-bottom:0;padding-top:0;text-align:center;"
              >

                <div
                  style="background:#ffffff;background-color:#ffffff;margin:0px auto;max-width:600px;"
                >
                  <table
                    align="center"
                    border="0"
                    cellpadding="0"
                    cellspacing="0"
                    role="presentation"
                    style="background:#ffffff;background-color:#ffffff;width:100%;"
                  >
                    <tbody>
                      <tr>
                        <td
                          style="direction:ltr;font-size:0px;padding:0 0 20px;padding-left:15px;padding-right:15px;text-align:center;padding-top: 20px;"
                        >

                          <div
                            class="mj-column-per-100 mj-outlook-group-fix"
                            style="font-size:15px;text-align:left;direction:ltr;display:inline-block;vertical-align:top;width:100%;background:#ffffff;background-color:#ffffff"
                          >
                            <!--start email_template -->
                            <h2>Hello ${option.email}</h2>
                            <p>A request has been received to change the
                              password for your
                              <strong>Gloopi</strong>
                              account.
                            </p>

                            <p>This link will expire in
                              <strong> 15 minute</strong>.</p>

                            <p style="margin-bottom:40px;">Click this link for
                              reset your password</p>

                            <a
                            href=${process.env.STORE_URL}/auth/forget-password/${option.token}
                              style="background:#22c55e;color:white;border:1px solid #22c55e; padding: 10px 15px; border-radius: 4px; text-decoration:none;"
                            >Reset Password </a>

                            <p style="margin-top: 50px;">If you did not initiate
                              this request, please contact us immediately at
                              <EMAIL></p>

                            <p
                              style="margin:0px auto; padding-bottom:0px; text-align:center;"
                            >Thank you
                              <h4
                                style="margin:1px; text-align:center;padding-bottom:0px;"
                              >Gloopi Team</h4></p>

                            <!--end email_template -->
                          </div>

                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      <!--[if mso | IE]></td></tr></table><![endif]-->

      <div
        style="background:#ffffff;background-color:#ffffff;margin:0px auto;max-width:600px;"
      >
        <table
          align="center"
          border="0"
          cellpadding="0"
          cellspacing="0"
          role="presentation"
          style="background:#ffffff;background-color:#ffffff;width:100%;"
        >
          <tbody>
            <tr>
              <td
                style="direction:ltr;font-size:0px;padding:0px;padding-left:15px;padding-right:15px;text-align:center;padding-top:0px;"
              >

                <div
                  class="mj-column-per-100 mj-outlook-group-fix"
                  style="margin-left:15px; font-size:15px;text-align:left;direction:ltr;display:inline-block;vertical-align:top;width:100%;background:#ffffff;background-color:#ffffff"
                >
                  <!--start email_template -->

                  <!--end email_template -->
                </div>

              </td>
            </tr>
          </tbody>
        </table>

        <table
          align="center"
          border="0"
          cellpadding="0"
          cellspacing="0"
          role="presentation"
          style="width:100%; margin-bottom:20px;"
        >
          <tbody>
            <tr>
              <td>

                <div style="margin:25px auto;max-width:600px;">
                  <table
                    align="center"
                    border="0"
                    cellpadding="0"
                    cellspacing="0"
                    role="presentation"
                    style="width:100%;"
                  >
                    <tbody>
                      <tr>
                        <td
                          style="direction:ltr;font-size:0px;margin-top:40px 0;text-align:center; border-top: 1px solid lightgray;"
                        >

                          <div style="margin:0px auto;max-width:600px;">
                            <table
                              align="center"
                              border="0"
                              cellpadding="0"
                              cellspacing="0"
                              role="presentation"
                              style="width:100%;"
                            >
                              <tbody>
                                <tr>
                                  <td
                                    style="direction:ltr;font-size:0px;padding:20px 0;text-align:center;"
                                  >
                                    <div
                                      class="mj-column-per-100 mj-outlook-group-fix"
                                      style="font-size:0px;text-align:left;direction:ltr;display:inline-block;vertical-align:top;width:100%;"
                                    >
                                      <table
                                        border="0"
                                        cellpadding="0"
                                        cellspacing="0"
                                        role="presentation"
                                        width="100%"
                                      >
                                        <tbody>
                                          <tr>
                                            <td
                                              style="vertical-align:top;padding:0;"
                                            >
                                              <table
                                                border="0"
                                                cellpadding="0"
                                                cellspacing="0"
                                                role="presentation"
                                                style
                                                width="100%"
                                              >
                                                <tbody>
                                                  <tr>
                                                    <td
                                                      align="center"
                                                      style="font-size:0px;padding:0px 25px;word-break:break-word;"
                                                    >
                                                      <div
                                                        style="padding-top:0px; font-family:'Helvetica Neue', Helvetica, Arial, sans-serif;font-size:11px;font-weight:400;line-height:16px;text-align:center;color:#8a8a8a;"
                                                      >
                                                        You are receiving this
                                                        email because you
                                                        registered with Gloopi
                                                        and agreed to receive
                                                        emails from us regarding
                                                        new features, events and
                                                        special offers.
                                                        <p
                                                          style="font-family:'Helvetica Neue', Helvetica, Arial, sans-serif;font-size:11px;font-weight:400;line-height:16px;text-align:center;color:#303030;"
                                                        >
                                                          &copy; Gloopi, All
                                                          Rights Reserved.</p></div>
                                                    </td>
                                                  </tr>

                                                </tbody>
                                              </table>
                                            </td>
                                          </tr>
                                        </tbody>
                                      </table>
                                    </div>
                                    <!--[if mso | IE]></td></tr></table><![endif]-->
                                  </td>
                                </tr>
                              </tbody>
                            </table>
                          </div>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
                <!--[if mso | IE]></td></tr></table><![endif]-->
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </body>

</html>

`;
};
module.exports = { forgetPasswordEmailBody };
